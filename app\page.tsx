import FlagGame from "@/components/FlagGame";
import { getDifficultyCountries } from "@/lib/data/difficultyCategories";
import { Country } from "@/lib/data/countries";

// Server-side function to get initial country
function getInitialCountry(): Country {
  const easyCountries = getDifficultyCountries("easy");
  const randomIndex = Math.floor(Math.random() * easyCountries.length);
  return easyCountries[randomIndex];
}

export default function Home() {
  const initialCountry = getInitialCountry();
  return <FlagGame initialCountry={initialCountry} />;
}
